'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { api } from '@/lib/api';
import { MSME, ScoreDetails } from '@/types';
import { ArrowLeft, Building2, MapPin, Calendar, TrendingUp, TrendingDown, Minus, AlertTriangle, BarChart3, Send, History, FileText, Phone, Mail, Globe, User, CreditCard, Target, Zap } from 'lucide-react';

interface MSMEDetailProps {
  msmeId: string;
}

export function MSMEDetail({ msmeId }: MSMEDetailProps) {
  const [msme, setMsme] = useState<MSME | null>(null);
  const [scoreDetails, setScoreDetails] = useState<ScoreDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchMSMEData() {
      try {
        setLoading(true);
        const [msmeData, scoreData] = await Promise.all([
          api.getMSME(msmeId),
          api.getMSMEScore(msmeId)
        ]);
        setMsme(msmeData);
        setScoreDetails(scoreData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch MSME data');
      } finally {
        setLoading(false);
      }
    }

    fetchMSMEData();
  }, [msmeId]);

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable': return <Minus className="h-4 w-4 text-gray-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <div className="grid gap-6 md:grid-cols-2">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-20 bg-muted rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading MSME Data</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!msme || !scoreDetails) return null;

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Analytics</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/portfolio">Portfolio</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-foreground font-medium">
                {msme.name}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                <Building2 className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                  {msme.name}
                </h1>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>{msme.location}</span>
                  </div>
                  <Badge variant="secondary" className="capitalize font-medium">
                    {msme.business_type}
                  </Badge>
                </div>
              </div>
            </div>

            {msme.tags.length > 0 && (
              <div className="flex gap-2 flex-wrap">
                {msme.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="flex gap-3">
            <Link href="/portfolio">
              <Button variant="outline" className="shadow-sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Portfolio
              </Button>
            </Link>
          </div>
        </div>

        {/* Score Overview */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-primary/5 to-primary/10">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-primary">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <BarChart3 className="h-5 w-5" />
                </div>
                Credit Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-5xl font-bold text-primary mb-3">
                {scoreDetails.current_score}
              </div>
              <div className="space-y-2">
                <Badge variant={getRiskBadgeVariant(scoreDetails.risk_band)} className="font-medium">
                  {getRiskLabel(scoreDetails.risk_band)} Risk
                </Badge>
                <div className="flex items-center gap-2">
                  {getTrendIcon(msme.score_trend)}
                  <span className="text-sm text-muted-foreground capitalize">
                    {msme.score_trend || 'stable'} trend
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <Target className="h-5 w-5 text-blue-500" />
                </div>
                Signals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-2">
                {scoreDetails.signals_count}
              </div>
              <p className="text-sm text-muted-foreground">
                Data points collected
              </p>
              <Progress value={(scoreDetails.signals_count / 20) * 100} className="mt-3 h-2" />
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <Calendar className="h-5 w-5 text-green-500" />
                </div>
                Last Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium mb-2">
                {new Date(msme.last_signal_date).toLocaleDateString()}
              </div>
              <p className="text-xs text-muted-foreground mb-3">
                {Math.floor((Date.now() - new Date(msme.last_signal_date).getTime()) / (1000 * 60 * 60 * 24))} days ago
              </p>
              {msme.recent_nudges > 0 && (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {msme.recent_nudges} alerts
                </Badge>
              )}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-purple-500/10 rounded-lg">
                  <Zap className="h-5 w-5 text-purple-500" />
                </div>
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" variant="outline" className="w-full" disabled>
                    <Send className="h-4 w-4 mr-2" />
                    Send Nudge
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Send risk alert notification</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" variant="outline" className="w-full" disabled>
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Download detailed report</p>
                </TooltipContent>
              </Tooltip>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Card className="border-0 shadow-lg">
          <Tabs defaultValue="score" className="w-full">
            <CardHeader className="pb-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="score">Score Analysis</TabsTrigger>
                <TabsTrigger value="profile">Business Profile</TabsTrigger>
                <TabsTrigger value="signals">Signal History</TabsTrigger>
                <TabsTrigger value="actions">Action Center</TabsTrigger>
              </TabsList>
            </CardHeader>

            <CardContent>
              <TabsContent value="score" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Credit Score Breakdown</h3>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary">Score Components</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                          <span className="font-medium">Base Score:</span>
                          <span className="font-mono text-lg font-bold text-green-600">
                            {scoreDetails.score_breakdown.base_score}
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                          <span className="font-medium">GST Penalty:</span>
                          <span className="font-mono text-lg font-bold text-red-600">
                            -{scoreDetails.score_breakdown.gst_penalty}
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                          <span className="font-medium">UPI Penalty:</span>
                          <span className="font-mono text-lg font-bold text-red-600">
                            -{scoreDetails.score_breakdown.upi_penalty}
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                          <span className="font-medium">Reviews Penalty:</span>
                          <span className="font-mono text-lg font-bold text-red-600">
                            -{scoreDetails.score_breakdown.reviews_penalty}
                          </span>
                        </div>
                        <Separator />
                        <div className="flex justify-between items-center p-4 bg-primary/10 rounded-lg border border-primary/20">
                          <span className="font-bold text-lg">Final Score:</span>
                          <span className="font-mono text-2xl font-bold text-primary">
                            {scoreDetails.current_score}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary">Analysis Details</h4>
                      <div className="space-y-3">
                        {Object.entries(scoreDetails.score_breakdown.details).map(([key, value]) => (
                          <div key={key} className="p-3 bg-muted/50 rounded-lg border">
                            <div className="font-medium capitalize text-sm text-muted-foreground mb-1">
                              {key.replace(/_/g, ' ')}
                            </div>
                            <div className="text-sm">{value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="profile" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Business Information</h3>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <h4 className="font-semibold mb-3 flex items-center gap-2">
                          <Building2 className="h-4 w-4" />
                          Basic Details
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Business Name:</span>
                            <span className="font-medium">{msme.name}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Type:</span>
                            <Badge variant="outline" className="capitalize">{msme.business_type}</Badge>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Location:</span>
                            <span className="font-medium">{msme.location}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Registered:</span>
                            <span className="font-medium">
                              {new Date(msme.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <h4 className="font-semibold mb-3 flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Contact Information
                        </h4>
                        <div className="space-y-3">
                          <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                            <Phone className="h-4 w-4 mr-2" />
                            Contact Number
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                            <Mail className="h-4 w-4 mr-2" />
                            Email Address
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                            <Globe className="h-4 w-4 mr-2" />
                            Website
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="signals" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Signal History</h3>
                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold">Recent Signals</h4>
                        <Badge variant="secondary">{scoreDetails.signals_count} total</Badge>
                      </div>
                      <div className="text-center py-8 text-muted-foreground">
                        <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-50" />
                        <p>Signal history will be displayed here</p>
                        <p className="text-sm">GST data, UPI transactions, reviews, etc.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="actions" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Action Center</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-4">
                      <h4 className="font-semibold">Risk Management</h4>
                      <div className="space-y-2">
                        <Button variant="outline" className="w-full justify-start" disabled>
                          <Send className="mr-2 h-4 w-4" />
                          Send Risk Alert
                        </Button>
                        <Button variant="outline" className="w-full justify-start" disabled>
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          Create Nudge
                        </Button>
                        <Button variant="outline" className="w-full justify-start" disabled>
                          <History className="mr-2 h-4 w-4" />
                          View Alert History
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold">Reports & Analysis</h4>
                      <div className="space-y-2">
                        <Button variant="outline" className="w-full justify-start" disabled>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate Credit Report
                        </Button>
                        <Button variant="outline" className="w-full justify-start" disabled>
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Score Trend Analysis
                        </Button>
                        <Button variant="outline" className="w-full justify-start" disabled>
                          <CreditCard className="mr-2 h-4 w-4" />
                          Export Data
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>

        {/* Footer */}
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground bg-muted/30 rounded-lg p-4">
          <Calendar className="h-4 w-4" />
          <span>Last updated: {new Date(scoreDetails.last_updated).toLocaleString()}</span>
        </div>
      </div>
    </TooltipProvider>
  );
}
