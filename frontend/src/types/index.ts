export interface MSME {
  msme_id: string;
  name: string;
  business_type: 'retail' | 'b2b' | 'manufacturing' | 'services';
  location: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_trend?: 'improving' | 'declining' | 'stable';
  signals_count: number;
  recent_nudges: number;
  last_signal_date: string;
  created_at: string;
  tags: string[];
}

export interface Analytics {
  total_msmes: number;
  total_signals: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  business_type_distribution: {
    [key: string]: number;
  };
  average_signals_per_msme: number;
  last_updated: string;
}

export interface ScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_breakdown: {
    base_score: number;
    gst_penalty: number;
    reviews_penalty: number;
    upi_penalty: number;
    details: {
      [key: string]: string;
    };
  };
  signals_count: number;
  last_updated: string;
}
